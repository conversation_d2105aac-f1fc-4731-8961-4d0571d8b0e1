import React, { useState, useEffect } from 'react';
import {
  Box,
  Button,
  Table,
  Thead,
  Tbody,
  Tr,
  Th,
  Td,
  TableContainer,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  FormControl,
  FormLabel,
  Input,
  Textarea,
  Select,
  VStack,
  HStack,
  Badge,
  IconButton,
  Alert,
  AlertIcon,
  Spinner,
  Text,
  useDisclosure,
  useToast,
  Link,
  Flex,
  Spacer,
  Tabs,
  TabList,
  TabPanels,
  Tab,
  TabPanel,
  Switch
} from '@chakra-ui/react';
import { FiEdit, FiTrash2, FiPlus, FiExternalLink } from 'react-icons/fi';
import { getAllWhatsNewLogs, createWhatsNewLog, updateWhatsNewLog, deleteWhatsNewLog } from '../services/whatsNew.service';
import Layout from './Layout';

const WhatsNewManagement = () => {
  const [logs, setLogs] = useState([]);
  const [loading, setLoading] = useState(true);
  const [editingLog, setEditingLog] = useState(null);
  const [deletingLog, setDeletingLog] = useState(null);
  const [formData, setFormData] = useState({
    type: 'dark',
    date: '',
    title_en: '',
    title_es: '',
    title_dom: '',
    description_en: '',
    description_es: '',
    description_dom: '',
    videoUrl: '',
    appVersion: '1.0.0',
    platform: 'both',
    forceUpdate: false
  });

  const { isOpen: isFormOpen, onOpen: onFormOpen, onClose: onFormClose } = useDisclosure();
  const { isOpen: isDeleteOpen, onOpen: onDeleteOpen, onClose: onDeleteClose } = useDisclosure();
  const toast = useToast();

  // Load logs on component mount
  useEffect(() => {
    loadLogs();
  }, []);

  const loadLogs = async () => {
    try {
      setLoading(true);
      const data = await getAllWhatsNewLogs();
      setLogs(data);
    } catch (err) {
      toast({
        title: 'Error loading logs',
        description: err.response?.data?.message || err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    } finally {
      setLoading(false);
    }
  };

  const handleOpenForm = (log = null) => {
    if (log) {
      setEditingLog(log);
      setFormData({
        type: log.type,
        date: log.date,
        title_en: log.title_en || '',
        title_es: log.title_es || '',
        title_dom: log.title_dom || '',
        description_en: log.description_en || '',
        description_es: log.description_es || '',
        description_dom: log.description_dom || '',
        videoUrl: log.videoUrl || '',
        appVersion: log.appVersion,
        platform: log.platform || 'both',
        forceUpdate: log.forceUpdate || false
      });
    } else {
      setEditingLog(null);
      setFormData({
        type: 'dark',
        date: new Date().toLocaleDateString('en-US', {
          year: 'numeric',
          month: 'short',
          day: '2-digit'
        }).toUpperCase(),
        title_en: '',
        title_es: '',
        title_dom: '',
        description_en: '',
        description_es: '',
        description_dom: '',
        videoUrl: '',
        appVersion: '1.0.0',
        platform: 'both',
        forceUpdate: false
      });
    }
    onFormOpen();
  };

  const handleCloseForm = () => {
    onFormClose();
    setEditingLog(null);
    setFormData({
      type: 'dark',
      date: '',
      title_en: '',
      title_es: '',
      title_dom: '',
      description_en: '',
      description_es: '',
      description_dom: '',
      videoUrl: '',
      appVersion: '1.0.0',
      platform: 'both'
    });
  };

  const handleSubmit = async () => {
    try {
      // Validate required fields
      if (!formData.type || !formData.date || !formData.title_en || !formData.title_es || !formData.title_dom ||
          !formData.description_en || !formData.description_es || !formData.description_dom || !formData.appVersion) {
        toast({
          title: 'Validation Error',
          description: 'Please fill in all required fields for all languages',
          status: 'error',
          duration: 3000,
          isClosable: true,
        });
        return;
      }

      if (editingLog) {
        await updateWhatsNewLog(editingLog.id, formData);
        toast({
          title: 'Success',
          description: 'What\'s New log updated successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      } else {
        await createWhatsNewLog(formData);
        toast({
          title: 'Success',
          description: 'What\'s New log created successfully',
          status: 'success',
          duration: 3000,
          isClosable: true,
        });
      }

      handleCloseForm();
      loadLogs();
    } catch (err) {
      toast({
        title: 'Error',
        description: err.response?.data?.message || err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const handleDelete = async () => {
    try {
      await deleteWhatsNewLog(deletingLog.id);
      toast({
        title: 'Success',
        description: 'What\'s New log deleted successfully',
        status: 'success',
        duration: 3000,
        isClosable: true,
      });
      onDeleteClose();
      setDeletingLog(null);
      loadLogs();
    } catch (err) {
      toast({
        title: 'Error',
        description: err.response?.data?.message || err.message,
        status: 'error',
        duration: 5000,
        isClosable: true,
      });
    }
  };

  const getTypeColor = (type) => {
    const colors = {
      dark: 'gray',
      red: 'red',
      cyan: 'cyan',
      purple: 'purple'
    };
    return colors[type] || 'gray';
  };

  const formatDate = (dateString) => {
    return new Date(dateString).toLocaleDateString();
  };

  if (loading) {
    return (
      <Layout title="What's New Management">
        <Box textAlign="center" py={10}>
          <Spinner size="xl" />
          <Text mt={4} fontSize="lg">Loading What's New logs...</Text>
        </Box>
      </Layout>
    );
  }

  return (
    <Layout title="What's New Management">
      <Box mb={6}>
        <Button
          leftIcon={<FiPlus />}
          colorScheme="blue"
          onClick={() => handleOpenForm()}
        >
          Add New Log
        </Button>
      </Box>

      <TableContainer>
        <Table variant="simple">
          <Thead>
            <Tr>
              <Th>Type</Th>
              <Th>Date</Th>
              <Th>Title</Th>
              <Th>Description</Th>
              <Th>App Version</Th>
              <Th>Video</Th>
              <Th>Created</Th>
              <Th>Actions</Th>
            </Tr>
          </Thead>
          <Tbody>
            {logs.map((log) => (
              <Tr key={log.id}>
                <Td>
                  <Badge
                    colorScheme={getTypeColor(log.type)}
                    textTransform="capitalize"
                  >
                    {log.type}
                  </Badge>
                </Td>
                <Td>{log.date}</Td>
                <Td>
                  <Text fontWeight="bold">
                    {log.title_en || log.title || 'No title'}
                  </Text>
                </Td>
                <Td>
                  <Text maxW="200px">
                    {(log.description_en || log.description || 'No description').length > 100
                      ? (log.description_en || log.description || 'No description').substring(0, 100) + '...'
                      : (log.description_en || log.description || 'No description')
                    }
                  </Text>
                </Td>
                <Td>
                  <Badge variant="outline">{log.appVersion}</Badge>
                </Td>
                <Td>
                  {log.videoUrl ? (
                    <IconButton
                      size="sm"
                      icon={<FiExternalLink />}
                      onClick={() => window.open(log.videoUrl, '_blank')}
                      title="View Video"
                    />
                  ) : (
                    '-'
                  )}
                </Td>
                <Td>{formatDate(log.createdAt)}</Td>
                <Td>
                  <HStack spacing={2}>
                    <IconButton
                      size="sm"
                      icon={<FiEdit />}
                      onClick={() => handleOpenForm(log)}
                      title="Edit"
                    />
                    <IconButton
                      size="sm"
                      icon={<FiTrash2 />}
                      onClick={() => {
                        setDeletingLog(log);
                        onDeleteOpen();
                      }}
                      title="Delete"
                      colorScheme="red"
                    />
                  </HStack>
                </Td>
              </Tr>
            ))}
            {logs.length === 0 && (
              <Tr>
                <Td colSpan={8} textAlign="center" py={8}>
                  <Text color="gray.500">
                    No What's New logs found. Create your first log to get started.
                  </Text>
                </Td>
              </Tr>
            )}
          </Tbody>
        </Table>
      </TableContainer>

      {/* Create/Edit Modal */}
      <Modal isOpen={isFormOpen} onClose={handleCloseForm} size="xl">
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>
            {editingLog ? 'Edit What\'s New Log' : 'Create New What\'s New Log'}
          </ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing={4}>
              <FormControl>
                <FormLabel>Type</FormLabel>
                <Select
                  value={formData.type}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                >
                  <option value="dark">Dark</option>
                  <option value="red">Red</option>
                  <option value="cyan">Cyan</option>
                  <option value="purple">Purple</option>
                </Select>
              </FormControl>

              <FormControl>
                <FormLabel>Date</FormLabel>
                <Input
                  value={formData.date}
                  onChange={(e) => setFormData({ ...formData, date: e.target.value })}
                  placeholder="e.g., JUN 13, 2023"
                />
                <Text fontSize="sm" color="gray.500">Display date as shown in the app</Text>
              </FormControl>

              <FormControl>
                <FormLabel>Platform Compatibility</FormLabel>
                <Select
                  value={formData.platform}
                  onChange={(e) => setFormData({ ...formData, platform: e.target.value })}
                >
                  <option value="both">Both (iOS & Android)</option>
                  <option value="ios">iOS Only</option>
                  <option value="android">Android Only</option>
                </Select>
              </FormControl>

              {/* Language Tabs */}
              <Box width="100%">
                <Text fontSize="md" fontWeight="bold" mb={2}>Content (Multi-Language)</Text>
                <Tabs variant="enclosed" colorScheme="blue">
                  <TabList>
                    <Tab>English</Tab>
                    <Tab>Spanish</Tab>
                    <Tab>Dominican</Tab>
                  </TabList>
                  <TabPanels>
                    <TabPanel>
                      <VStack spacing={4}>
                        <FormControl isRequired>
                          <FormLabel>Title (English)</FormLabel>
                          <Input
                            value={formData.title_en}
                            onChange={(e) => setFormData({ ...formData, title_en: e.target.value })}
                            placeholder="e.g., NEW FEATURE"
                          />
                        </FormControl>
                        <FormControl isRequired>
                          <FormLabel>Description (English)</FormLabel>
                          <Textarea
                            value={formData.description_en}
                            onChange={(e) => setFormData({ ...formData, description_en: e.target.value })}
                            placeholder="Describe the new feature or update"
                            rows={3}
                          />
                        </FormControl>
                      </VStack>
                    </TabPanel>
                    <TabPanel>
                      <VStack spacing={4}>
                        <FormControl isRequired>
                          <FormLabel>Title (Spanish)</FormLabel>
                          <Input
                            value={formData.title_es}
                            onChange={(e) => setFormData({ ...formData, title_es: e.target.value })}
                            placeholder="e.g., NUEVA FUNCIÓN"
                          />
                        </FormControl>
                        <FormControl isRequired>
                          <FormLabel>Description (Spanish)</FormLabel>
                          <Textarea
                            value={formData.description_es}
                            onChange={(e) => setFormData({ ...formData, description_es: e.target.value })}
                            placeholder="Describe la nueva función o actualización"
                            rows={3}
                          />
                        </FormControl>
                      </VStack>
                    </TabPanel>
                    <TabPanel>
                      <VStack spacing={4}>
                        <FormControl isRequired>
                          <FormLabel>Title (Dominican Spanish)</FormLabel>
                          <Input
                            value={formData.title_dom}
                            onChange={(e) => setFormData({ ...formData, title_dom: e.target.value })}
                            placeholder="e.g., NUEVA FUNCIÓN"
                          />
                        </FormControl>
                        <FormControl isRequired>
                          <FormLabel>Description (Dominican Spanish)</FormLabel>
                          <Textarea
                            value={formData.description_dom}
                            onChange={(e) => setFormData({ ...formData, description_dom: e.target.value })}
                            placeholder="Describe la nueva función o actualización"
                            rows={3}
                          />
                        </FormControl>
                      </VStack>
                    </TabPanel>
                  </TabPanels>
                </Tabs>
              </Box>

              <FormControl>
                <FormLabel>Video URL (Optional)</FormLabel>
                <Input
                  value={formData.videoUrl}
                  onChange={(e) => setFormData({ ...formData, videoUrl: e.target.value })}
                  placeholder="https://www.youtube.com/watch?v=..."
                />
              </FormControl>

              <FormControl>
                <FormLabel>App Version</FormLabel>
                <Input
                  value={formData.appVersion}
                  onChange={(e) => setFormData({ ...formData, appVersion: e.target.value })}
                  placeholder="e.g., 1.0.14"
                />
                <Text fontSize="sm" color="gray.500">Minimum app version required for this feature</Text>
              </FormControl>

              <FormControl>
                <HStack justify="space-between" align="center">
                  <VStack align="start" spacing={1}>
                    <FormLabel mb={0}>Force Update</FormLabel>
                    <Text fontSize="sm" color="gray.500">
                      When enabled, users cannot dismiss the update notification
                    </Text>
                  </VStack>
                  <Switch
                    isChecked={formData.forceUpdate}
                    onChange={(e) => setFormData({ ...formData, forceUpdate: e.target.checked })}
                    colorScheme="red"
                    size="lg"
                  />
                </HStack>
              </FormControl>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={handleCloseForm}>
              Cancel
            </Button>
            <Button colorScheme="blue" onClick={handleSubmit}>
              {editingLog ? 'Update' : 'Create'}
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>

      {/* Delete Confirmation Modal */}
      <Modal isOpen={isDeleteOpen} onClose={onDeleteClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Confirm Delete</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <Text>
              Are you sure you want to delete the log "{deletingLog?.title}"? This action cannot be undone.
            </Text>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onDeleteClose}>
              Cancel
            </Button>
            <Button colorScheme="red" onClick={handleDelete}>
              Delete
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Layout>
  );
};

export default WhatsNewManagement;
