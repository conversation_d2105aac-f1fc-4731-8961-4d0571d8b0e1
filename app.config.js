require('dotenv').config();

module.exports = {
  expo: {
    name: "TapTrap",
    slug: "tap-trap",
    version: "1.0.18",
    orientation: "portrait",
    icon: "./assets/images/icon.png",
    scheme: "myapp",
    userInterfaceStyle: "automatic",
    assetBundlePatterns: [
      "**/*"
    ],
    newArchEnabled: false,
    ios: {
      supportsTablet: true,
      buildNumber: "1",
      bundleIdentifier: "app.taptrap",
      infoPlist: {
        ITSAppUsesNonExemptEncryption: false,
        NSAppTransportSecurity: {
          NSAllowsArbitraryLoads: true
        },
        GADApplicationIdentifier: "ca-app-pub-8401892082498077~3768284188",
        NSUserNotificationsUsageDescription: "TapTrap would like to send you notifications about new content and features to enhance your gaming experience."
      },

    },
    android: {
      adaptiveIcon: {
        foregroundImage: "./assets/images/adaptive-icon.png"
      },
      googleServicesFile: "./google-services.json",
      package: "app.taptrap",
      versionCode: 12,
      permissions: [
        "BILLING",
        "android.permission.POST_NOTIFICATIONS",
        "android.permission.RECEIVE_BOOT_COMPLETED",
        "android.permission.VIBRATE"
      ],
      intentFilters: [
        {
          action: "android.intent.action.MAIN",
          category: ["android.intent.category.LAUNCHER"]
        }
      ],

    },
    web: {
      bundler: "metro",
      output: "static",
      favicon: "./assets/images/favicon.png"
    },
    plugins: [
      "expo-router",
      [
        "expo-splash-screen",
        {
          "image": "./assets/images/splash.png",
          "imageWidth": 150,
          "resizeMode": "contain",
          "backgroundColor": "#131416"
        }
      ],
      [
        "expo-font",
        {
          "fonts": [
            "./assets/fonts/Melindya.ttf",
            "./assets/fonts/Nunito-Bold.ttf",
            "./assets/fonts/Nunito-SemiBold.ttf"
          ]
        }
      ],
      "expo-localization",
      [
        "expo-notifications",
        {
          "color": "#ffffff"
        }
      ],
      "expo-secure-store",
      [
        "react-native-google-mobile-ads",
        {
          "androidAppId": "ca-app-pub-8401892082498077~9640392112",
          "iosAppId": "ca-app-pub-8401892082498077~3768284188"
        }
      ],
      [
        "@sentry/react-native/expo",
        {
          "url": "https://sentry.io/",
          "project": "react-native",
          "organization": "personal-gy5"
        }
      ]
    ],
    experiments: {
      typedRoutes: true
    },
    extra: {
      apiEncryptionKey: process.env.API_ENCRYPTION_KEY,
      revenueCatIosKey: process.env.REVENUECAT_IOS_KEY,
      revenueCatAndroidKey: process.env.REVENUECAT_ANDROID_KEY,
      admobRewardedAdUnitIdAndroid: process.env.ADMOB_REWARDED_AD_UNIT_ID_ANDROID,
      admobRewardedAdUnitIdIos: process.env.ADMOB_REWARDED_AD_UNIT_ID_IOS,
      router: {
        origin: false
      },
      eas: {
        projectId: "c488beec-b0b8-4ad1-b82a-4e646c9103a4"
      }
    },
    owner: "eliezerpujols",
    runtimeVersion: "1.0.8",
    updates: {
      url: "https://u.expo.dev/c488beec-b0b8-4ad1-b82a-4e646c9103a4"
    }
  }
};
